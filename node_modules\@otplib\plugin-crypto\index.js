/**
 * @otplib/plugin-crypto
 *
 * <AUTHOR> <<EMAIL>>
 * @version: 12.0.1
 * @license: MIT
 **/
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }

var crypto = _interopDefault(require('crypto'));

const createDigest = (algorithm, hmacKey, counter) => {
  const hmac = crypto.createHmac(algorithm, Buffer.from(hmacKey, 'hex'));
  const digest = hmac.update(Buffer.from(counter, 'hex')).digest();
  return digest.toString('hex');
};
const createRandomBytes = (size, encoding) => {
  return crypto.randomBytes(size).toString(encoding);
};

exports.createDigest = createDigest;
exports.createRandomBytes = createRandomBytes;
