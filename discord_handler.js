const { Client, BitField } = require('discord.js-selfbot-v13');

class DiscordHandler {
    constructor(token, server_id) {
        this.client = new Client({ checkUpdate: false });
        this.token = token;
        this.server_id = server_id;
        this.guild = null;
        this.execution_timeout = 30000;
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            this.client.on('ready', async () => {
                console.log(`logged in as ${this.client.user.tag}`);
                this.guild = this.client.guilds.cache.get(this.server_id);
                
                if (!this.guild) {
                    reject(new Error(`could not find server with id: ${this.server_id}`));
                    return;
                }
                
                console.log(`successfully attached to server: "${this.guild.name}" (id: ${this.guild.id})`);
                
                try {
                    console.log('fetching initial server data...');
                    await this.refresh_server_data();
                    console.log('server data fetched. discord handler ready.');
                    resolve();
                } catch (error) {
                    reject(new Error(`failed to fetch server data: ${error.message}`));
                }
            });

            this.client.login(this.token).catch(error => {
                reject(new Error(`failed to log in to discord: ${error.message}`));
            });
        });
    }

    async refresh_server_data() {
        if (!this.guild) throw new Error('guild not initialized');

        try {
            await this.guild.members.fetch();
            await this.guild.roles.fetch();
            await this.guild.channels.fetch();
        } catch (error) {
            console.error(`[discord] error refreshing server data: ${error.message}`);
            throw error;
        }
    }

    get_server_state() {
        if (!this.guild) throw new Error('guild not initialized');
        
        const channels = this.guild.channels.cache
            .map(c => `- ${c.name} (id: ${c.id}, type: ${c.type.toString().replace('GUILD_', '').toLowerCase()})`)
            .join('\n');
            
        const roles = this.guild.roles.cache
            .map(r => `- ${r.name} (id: ${r.id}, position: ${r.position}, hoisted: ${r.hoist})`)
            .join('\n');
            
        return { 
            channels, 
            roles, 
            guild_name: this.guild.name, 
            guild_id: this.guild.id 
        };
    }

    get_client_info() {
        if (!this.client.user) throw new Error('client not ready');
        
        return {
            user_tag: this.client.user.tag,
            user_id: this.client.user.id
        };
    }

    async execute_code_safely(code_string) {
        console.log(`\n> generated code:\n\`\`\`javascript\n${code_string}\n\`\`\``);

        let captured_logs = [];
        const original_console_log = console.log;

        console.log = (...args) => {
            const message = args.map(a => (typeof a === 'object' ? JSON.stringify(a, null, 2) : String(a))).join(' ');
            captured_logs.push(message);
            original_console_log.apply(console, ['[code log]', ...args]);
        };

        try {
            const enhanced_code = this.enhance_code_with_safety(code_string);

            await Promise.race([
                eval(`(async () => {
                    const guild = this.guild;
                    const client = this.client;
                    const BitField = require('discord.js-selfbot-v13').BitField;
                    ${enhanced_code}
                }).call(this)`),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('code execution timeout')), this.execution_timeout)
                )
            ]);

            console.log = original_console_log;
            await this.refresh_server_data();

            const result_message = "code executed successfully.";
            return captured_logs.length > 0 ?
                `${result_message} console output: ${captured_logs.join('\n')}` :
                result_message;

        } catch (error) {
            console.log = original_console_log;
            throw new Error(`execution failed: ${error.message}`);
        }
    }

    enhance_code_with_safety(code_string) {
        const safety_enhancements = `
const safe_get_id = (obj) => {
    if (typeof obj === 'string') return obj;
    if (obj && typeof obj === 'object' && obj.id) return obj.id;
    throw new Error('invalid object passed - expected string id or object with id property');
};

const validate_embed = (embed) => {
    if (!embed) return false;
    if (typeof embed !== 'object') return false;
    if (!embed.title && !embed.description) {
        console.error('embed validation failed: must have title or description');
        return false;
    }
    if (embed.title && embed.title.length > 256) {
        console.error('embed validation failed: title too long (max 256 chars)');
        return false;
    }
    if (embed.description && embed.description.length > 4096) {
        console.error('embed validation failed: description too long (max 4096 chars)');
        return false;
    }
    return true;
};

const validate_message_content = (content) => {
    if (!content) return false;
    if (typeof content === 'string' && content.trim().length === 0) return false;
    if (typeof content === 'object') {
        if (!content.content && !content.embeds && !content.components) return false;
        if (content.embeds && content.embeds.length > 0) {
            return content.embeds.every(embed => validate_embed(embed));
        }
    }
    return true;
};

const safe_send_message = async (channel, message_data) => {
    if (!validate_message_content(message_data)) {
        throw new Error('message validation failed: empty or invalid content');
    }

    try {
        return await channel.send(message_data);
    } catch (error) {
        console.error(\`failed to send message: \${error.message}\`);
        throw error;
    }
};

const validate_role_permissions = (permissions) => {
    const dangerous_perms = ['ADMINISTRATOR', 'MANAGE_GUILD', 'MANAGE_ROLES', 'BAN_MEMBERS'];
    const has_dangerous = permissions.some(perm => dangerous_perms.includes(perm));

    if (has_dangerous) {
        console.warn('warning: creating role with potentially dangerous permissions');
    }

    return permissions;
};

const create_role_safely = async (guild, role_data) => {
    const existing_role = guild.roles.cache.find(role => role.name.toLowerCase() === role_data.name.toLowerCase());
    if (existing_role) {
        console.log(\`role '\${role_data.name}' already exists with id: \${existing_role.id}\`);
        return existing_role;
    }

    if (role_data.permissions) {
        role_data.permissions = validate_role_permissions(role_data.permissions);
    }

    try {
        const new_role = await guild.roles.create(role_data);
        console.log(\`successfully created role '\${role_data.name}' with id: \${new_role.id}\`);
        return new_role;
    } catch (error) {
        console.error(\`failed to create role '\${role_data.name}': \${error.message}\`);
        throw error;
    }
};

const create_channel_safely = async (guild, channel_data) => {
    const existing_channel = guild.channels.cache.find(channel => channel.name.toLowerCase() === channel_data.name.toLowerCase());
    if (existing_channel) {
        console.log(\`channel '\${channel_data.name}' already exists with id: \${existing_channel.id}\`);
        return existing_channel;
    }

    try {
        const new_channel = await guild.channels.create(channel_data);
        console.log(\`successfully created channel '\${channel_data.name}' with id: \${new_channel.id}\`);
        return new_channel;
    } catch (error) {
        console.error(\`failed to create channel '\${channel_data.name}': \${error.message}\`);
        throw error;
    }
};

const suggest_external_bot_command = (bot_name, command, description) => {
    console.log(\`suggestion: use \${bot_name} bot with command: \${command}\`);
    console.log(\`description: \${description}\`);
    console.log(\`note: this requires the bot to be present in the server\`);
};

`;

        return safety_enhancements + code_string;
    }

    destroy() {
        if (this.client) {
            this.client.destroy();
        }
    }
}

module.exports = DiscordHandler;
