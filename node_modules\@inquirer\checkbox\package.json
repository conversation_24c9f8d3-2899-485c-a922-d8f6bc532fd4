{"name": "@inquirer/checkbox", "version": "4.2.0", "description": "Inquirer checkbox prompt", "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh"], "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/main/packages/checkbox/README.md", "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["dist"], "scripts": {"attw": "attw --pack", "tsc": "tshy"}, "dependencies": {"@inquirer/core": "^10.1.15", "@inquirer/figures": "^1.0.13", "@inquirer/type": "^3.0.8", "ansi-escapes": "^4.3.2", "yoctocolors-cjs": "^2.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.2", "@inquirer/testing": "^2.1.49", "tshy": "^3.0.2"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "tshy": {"exclude": ["src/**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "peerDependencies": {"@types/node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}, "gitHead": "c1a755fe8b50377b685ea5951e0794985ce8d356"}