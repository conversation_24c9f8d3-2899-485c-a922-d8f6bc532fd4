# @otplib/preset-v11

> Preset with v11.x compatibility adapter for otplib@v12.x

See our [project readme][project-v-readme] for more information
or visit the [demo website][project-v-site].

## Install

```bash
npm install --save @otplib/preset-v11
```

## License

`@otplib/preset-v11` is [MIT licensed][project-license]

[project-license]: https://github.com/yeojz/otplib/blob/master/LICENSE
[project-v-readme]: https://github.com/yeojz/otplib/blob/master/README.md
[project-v-site]: https://otplib.yeojz.dev
