const { pipeline } = require('@huggingface/transformers');

class AIHandler {
    constructor() {
        this.pipe = null;
        this.max_retries = 3;
        this.retry_delay = 1000;
        this.conversation_history = [];
        this.tools = this.define_tools();
        this.tool_map = this.create_tool_map();
    }

    async initialize() {
        console.log('initializing local kimi k2 model...');
        this.pipe = await pipeline("text-generation", "moonshotai/Kimi-K2-Instruct", {
            trust_remote_code: true,
            device: 'cpu'
        });
        console.log('kimi k2 model loaded successfully');
    }

    define_tools() {
        return [
            {
                type: "function",
                function: {
                    name: "execute_discord_code",
                    description: "Execute JavaScript code for Discord server management",
                    parameters: {
                        type: "object",
                        required: ["code"],
                        properties: {
                            code: { type: "string", description: "JavaScript code to execute using guild and client variables" },
                            description: { type: "string", description: "What this code does" }
                        }
                    }
                }
            }
        ];
    }

    create_tool_map() {
        return {
            execute_discord_code: this.tool_execute_discord_code.bind(this)
        };
    }

    async tool_execute_discord_code(params, discord_handler) {
        try {
            console.log(`[executing] ${params.description || 'discord action'}`);
            console.log(`[code] ${params.code}`);

            const result = await discord_handler.execute_code_safely(params.code);
            return { success: true, message: result };
        } catch (error) {
            return { success: false, message: `execution failed: ${error.message}` };
        }
    }

    async process_with_tools(user_message, discord_handler) {
        const messages = [
            {
                role: "system",
                content: `you are kimi, a discord server management ai. you execute javascript code to manage discord servers.

**rules:**
- NEVER send chat messages - only execute code
- use guild and client variables (already available)
- be smart about permissions and security
- keep code concise and effective

**server:** ${discord_handler.guild.name}

use the execute_discord_code tool to accomplish the user's goal.`
            },
            {
                role: "user",
                content: user_message
            }
        ];

        try {
            const response = await this.pipe(messages, {
                max_new_tokens: 512,
                temperature: 0.2,
                tools: this.tools,
                tool_choice: "auto"
            });

            const choice = response[0];

            if (choice.message && choice.message.tool_calls) {
                for (const tool_call of choice.message.tool_calls) {
                    const tool_name = tool_call.function.name;
                    const tool_args = JSON.parse(tool_call.function.arguments);
                    const tool_function = this.tool_map[tool_name];

                    if (tool_function) {
                        const tool_result = await tool_function(tool_args, discord_handler);
                        console.log(`[result] ${tool_result.message}`);
                        return tool_result;
                    }
                }
            } else if (choice.message && choice.message.content) {
                console.log(`[ai] ${choice.message.content}`);
            }
        } catch (error) {
            console.error(`[ai error] ${error.message}`);
            return { success: false, message: `ai error: ${error.message}` };
        }

        return { success: true, message: "complete" };
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;

        return `you are an expert discord server management ai with advanced intelligence and security awareness. you prioritize server safety, proper permissions, and native discord features.

**critical instructions:**

1. **action protocol:**
   - your response **must** be a single, valid json object
   - the json must have a 'type' field: \`execute\`, \`ask\`, \`inform\`, \`error\`

2. **intelligent decision making:**
   - **permission safety:** give appropriate permissions, not admin unless explicitly requested
   - **feature preference:** use native discord features first, external bots when needed
   - **be concise:** short responses, minimal explanations

3. **code generation rules:**
   - the 'code' field must be a **valid json string** (use "\\n" for newlines)
   - **keep code short:** maximum 30 lines per response
   - **validation required:** always validate embeds and messages before sending
   - **variable naming:** use provided 'guild' and 'client' variables directly
   - **comprehensive error handling:** wrap actions in try-catch blocks

4. **external bot usage:**
   - **never access bot caches** - external bots are separate applications
   - **use direct slash commands** - send commands as regular messages to channels
   - **format:** send "/command parameters" as text message to appropriate channel
   - **examples:** "/automod spam enable", "/role create VIP", "/logging #modlogs"

5. **permission inference:**
   - **vip roles:** moderate permissions (priority speaker, external emojis)
   - **staff roles:** moderation permissions (kick, timeout, manage messages)
   - **ping roles:** no special permissions
   - **admin roles:** only when explicitly requested

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"
- available variables: guild, client, BitField

**current server state:**
channels:
${channels}

roles:
${roles}

**remember:** be concise, secure, and practical. use external bots via slash commands when needed.`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text.substring(0, 1000)}${text.length > 1000 ? '...[truncated]' : ''}\n\`\`\``);

        try {
            let json_string;
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);

            if (json_match) {
                json_string = json_match[1];
            } else {
                json_string = text.trim();
            }

            if (json_string.length > 10000) {
                console.error(`[error] response too long (${json_string.length} chars), likely malformed`);
                return {
                    type: "error",
                    error_message: "response too long - please provide a more concise solution or break it into smaller steps"
                };
            }

            const parsed = JSON.parse(json_string);

            if (parsed.code && parsed.code.length > 5000) {
                console.error(`[error] generated code too long (${parsed.code.length} chars)`);
                return {
                    type: "error",
                    error_message: "generated code too long - please break the solution into smaller, focused steps"
                };
            }

            return parsed;

        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);

            if (text.includes('```') && text.includes('console.log')) {
                return {
                    type: "error",
                    error_message: "response contains unescaped code blocks - please provide a proper json response with escaped strings"
                };
            }

            return {
                type: "error",
                error_message: "internal error: my own response was not valid json. please ensure your response is a valid json object."
            };
        }
    }

    async send_message_with_retry(message, attempt = 1) {
        try {
            const full_prompt = this.build_conversation_prompt(message);

            const result = await generateText({
                model: this.model,
                prompt: full_prompt,
                temperature: 0.1,
                maxTokens: 4000
            });

            return await this.parse_response(result.text);
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);

            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return {
                    type: "error",
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}`
                };
            }
        }
    }

    initialize_conversation(guild_info, client_info) {
        this.conversation_history = [{
            role: "system",
            content: this.create_system_prompt(guild_info, client_info)
        }];
    }

    build_conversation_prompt(user_message) {
        const messages = [...this.conversation_history, {
            role: "user",
            content: user_message
        }];

        return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
    }

    add_to_conversation(role, content) {
        this.conversation_history.push({ role, content });

        if (this.conversation_history.length > 20) {
            this.conversation_history = [
                this.conversation_history[0],
                ...this.conversation_history.slice(-19)
            ];
        }
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        const error_analysis = this.analyze_error(error_message, failed_code);

        return `**ERROR CORRECTION**

goal: "${initial_goal}"
error: "${error_message}"

${error_analysis}

**fix guidelines:**
- validate embeds (title OR description required)
- use external bots via slash commands as text messages
- never access external bot caches
- keep solutions simple and short

**failed code:**
\`\`\`javascript
${failed_code}
\`\`\`

provide a short, working fix.`;
    }

    analyze_error(error_message, failed_code) {
        const error_lower = error_message.toLowerCase();

        if (error_lower.includes('cannot send an empty message')) {
            return `❌ EMPTY MESSAGE ERROR
- discord requires messages to have content, embeds, or components
- your embed is likely missing both title and description
- solution: use validate_embed() function and ensure embeds have title OR description
- fix: add proper title/description to your embed before sending`;
        }

        if (error_lower.includes('already been declared') || error_lower.includes('identifier') && error_lower.includes('declared')) {
            return `❌ VARIABLE REDECLARATION ERROR
- you're trying to declare a variable that already exists in the execution context
- the execution context provides: guild, client, BitField
- solution: use these variables directly or choose different variable names`;
        }

        if (error_lower.includes('onboarding') || (error_lower.includes('cannot read properties of undefined') && failed_code.includes('onboarding'))) {
            return `❌ ONBOARDING API LIMITATION
- guild.onboarding is not available in discord.js-selfbot-v13
- selfbots cannot access server onboarding features
- solution: suggest using discord's native server settings or external bot integration
- alternative: recommend manual setup through discord client`;
        }

        if (error_lower.includes('permission') || error_lower.includes('forbidden') || error_lower.includes('missing access')) {
            return `❌ PERMISSION ERROR
- the bot lacks required permissions for this operation
- solution: check user permissions first or suggest manual alternatives
- consider: use less privileged approaches or external bot commands`;
        }

        if (error_lower.includes('invalid form body') || error_lower.includes('snowflake')) {
            return `❌ DATA TYPE ERROR
- you're passing objects where string ids are expected
- solution: always use .id property when referencing discord entities
- fix: use safe_get_id() function for all discord object references`;
        }

        if (error_lower.includes('timeout') || error_lower.includes('network')) {
            return `❌ NETWORK/TIMEOUT ERROR
- the operation took too long or network failed
- solution: add retry logic or break into smaller operations`;
        }

        if (error_lower.includes('missing permissions') || error_lower.includes('insufficient permissions')) {
            return `❌ INSUFFICIENT PERMISSIONS ERROR
- the selfbot account lacks the required permissions
- solution: check role hierarchy and permissions before attempting actions
- alternative: suggest external bot usage for privileged operations`;
        }

        return `❌ UNKNOWN ERROR
- analyze the error message carefully
- use validation functions before api calls
- consider if the feature requires external bot integration
- provide secure alternatives that don't compromise server safety`;
    }
}

module.exports = AIHandler;
