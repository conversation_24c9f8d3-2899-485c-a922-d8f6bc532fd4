const { openrouter } = require('@openrouter/ai-sdk-provider');
const { generateText } = require('ai');

class AIHandler {
    constructor(api_key) {
        this.api_key = api_key;
        this.model = openrouter('moonshotai/kimi-k2:free');
        this.max_retries = 3;
        this.retry_delay = 1000;
        this.conversation_history = [];
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;

        return `you are an expert discord server management ai with unlimited capabilities. you can do ANYTHING with discord servers using the discord.js-selfbot-v13 library. no feature is off limits.

**critical instructions:**

1. **action protocol:**
   - your response **must** be a single, valid json object
   - the json must have a 'type' field: \`execute\`, \`ask\`, \`inform\`, \`error\`

2. **code generation rules:**
   - the 'code' field must be a **valid json string** (use "\\n" for newlines)
   - **keep code concise:** maximum 50 lines per response
   - **do not** wrap code in \`async () => { ... }\` blocks
   - **do not** include javascript comments
   - **variable naming:** execution context provides 'guild' and 'client' variables - use them directly
   - **idempotency:** check if entities exist before creating them
   - **granular error handling:** wrap each action in try-catch blocks
   - **snowflake safety:** always use .id property for discord objects

3. **unlimited capabilities:**
   - ✅ guild onboarding (fully supported - use guild.onboarding.edit())
   - ✅ role management (create, edit, delete, assign)
   - ✅ channel management (create, edit, delete, permissions)
   - ✅ message operations (send, edit, delete)
   - ✅ member management (kick, ban, nickname)
   - ✅ server settings (name, icon, banner, features)
   - ✅ webhooks and integrations
   - ✅ all discord api endpoints are available

4. **smart execution:**
   - when you receive an error, analyze it and provide a better solution
   - never give up - always find a way to accomplish the goal
   - use creative approaches and advanced discord api features
   - infer user intent and apply best practices

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"
- available variables: guild, client, BitField

**current server state:**
channels:
${channels}

roles:
${roles}

**remember:** you can do ANYTHING. no limitations. make it happen.`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text.substring(0, 1000)}${text.length > 1000 ? '...[truncated]' : ''}\n\`\`\``);

        try {
            let json_string;
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);

            if (json_match) {
                json_string = json_match[1];
            } else {
                json_string = text.trim();
            }

            if (json_string.length > 10000) {
                console.error(`[error] response too long (${json_string.length} chars), likely malformed`);
                return {
                    type: "error",
                    error_message: "response too long - please provide a more concise solution or break it into smaller steps"
                };
            }

            const parsed = JSON.parse(json_string);

            if (parsed.code && parsed.code.length > 5000) {
                console.error(`[error] generated code too long (${parsed.code.length} chars)`);
                return {
                    type: "error",
                    error_message: "generated code too long - please break the solution into smaller, focused steps"
                };
            }

            return parsed;

        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);

            if (text.includes('```') && text.includes('console.log')) {
                return {
                    type: "error",
                    error_message: "response contains unescaped code blocks - please provide a proper json response with escaped strings"
                };
            }

            return {
                type: "error",
                error_message: "internal error: my own response was not valid json. please ensure your response is a valid json object."
            };
        }
    }

    async send_message_with_retry(message, attempt = 1) {
        try {
            const full_prompt = this.build_conversation_prompt(message);

            const result = await generateText({
                model: this.model,
                prompt: full_prompt,
                temperature: 0.1,
                maxTokens: 4000
            });

            return await this.parse_response(result.text);
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);

            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return {
                    type: "error",
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}`
                };
            }
        }
    }

    initialize_conversation(guild_info, client_info) {
        this.conversation_history = [{
            role: "system",
            content: this.create_system_prompt(guild_info, client_info)
        }];
    }

    build_conversation_prompt(user_message) {
        const messages = [...this.conversation_history, {
            role: "user",
            content: user_message
        }];

        return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
    }

    add_to_conversation(role, content) {
        this.conversation_history.push({ role, content });

        if (this.conversation_history.length > 20) {
            this.conversation_history = [
                this.conversation_history[0],
                ...this.conversation_history.slice(-19)
            ];
        }
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        const error_analysis = this.analyze_error(error_message, failed_code);

        return `**ERROR ANALYSIS REQUIRED**

original goal: "${initial_goal}"
error encountered: "${error_message}"

**detailed error analysis:**
${error_analysis}

**your task:**
1. analyze the specific error type and root cause
2. if the feature is not supported in discord.js-selfbot-v13, provide an alternative approach
3. if it's a variable conflict, use different variable names
4. if it's a permission issue, suggest checking permissions first
5. if it's an api limitation, explain why and offer workarounds

**execution context reminders:**
- variables 'guild' and 'client' are already available - never redeclare them
- discord.js-selfbot-v13 has limited api support compared to the full discord.js library
- always check if entities exist before creating them
- wrap each operation in try-catch blocks for granular error handling

**failed code for reference:**
\`\`\`javascript
${failed_code}
\`\`\`

provide a corrected solution or explain why the goal cannot be achieved with the current library limitations.`;
    }

    analyze_error(error_message, failed_code) {
        const error_lower = error_message.toLowerCase();

        if (error_lower.includes('already been declared') || error_lower.includes('identifier') && error_lower.includes('declared')) {
            return `❌ VARIABLE REDECLARATION ERROR
- you're trying to declare a variable that already exists in the execution context
- the execution context provides: guild, client, BitField
- solution: use these variables directly or choose different variable names`;
        }

        if (error_lower.includes('onboarding') || error_lower.includes('not found')) {
            return `❌ API LIMITATION ERROR
- the requested feature is not available in discord.js-selfbot-v13
- selfbots have limited api access compared to regular bots
- solution: provide alternative approaches or explain the limitation`;
        }

        if (error_lower.includes('permission') || error_lower.includes('forbidden')) {
            return `❌ PERMISSION ERROR
- the bot lacks required permissions for this operation
- solution: check permissions first or suggest manual alternatives`;
        }

        if (error_lower.includes('invalid form body') || error_lower.includes('snowflake')) {
            return `❌ DATA TYPE ERROR
- you're passing objects where string ids are expected
- solution: always use .id property when referencing discord entities`;
        }

        if (error_lower.includes('timeout') || error_lower.includes('network')) {
            return `❌ NETWORK/TIMEOUT ERROR
- the operation took too long or network failed
- solution: add retry logic or break into smaller operations`;
        }

        return `❌ UNKNOWN ERROR
- analyze the error message carefully
- check the discord.js-selfbot-v13 documentation for supported features
- provide alternative approaches if the primary method fails`;
    }
}

module.exports = AIHandler;
