const { openrouter } = require('@openrouter/ai-sdk-provider');
const { generateText } = require('ai');

class AIHandler {
    constructor(api_key) {
        this.api_key = api_key;
        this.model = openrouter('moonshotai/kimi-k2:free');
        this.max_retries = 3;
        this.retry_delay = 1000;
        this.conversation_history = [];
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;

        return `you are an expert discord server management ai with advanced intelligence and security awareness. you prioritize server safety, proper permissions, and native discord features.

**critical instructions:**

1. **action protocol:**
   - your response **must** be a single, valid json object
   - the json must have a 'type' field: \`execute\`, \`ask\`, \`inform\`, \`error\`

2. **intelligent decision making:**
   - **permission safety:** never give administrator permissions unless explicitly requested and justified
   - **role hierarchy:** understand role positioning and server structure
   - **feature preference:** always use native discord features first (buttons, select menus, slash commands)
   - **bot integration:** only suggest external bots when native features are insufficient
   - **security first:** protect server from potential abuse or misconfiguration

3. **code generation rules:**
   - the 'code' field must be a **valid json string** (use "\\n" for newlines)
   - **validation required:** always validate data before sending to discord api
   - **error prevention:** check for empty messages, invalid embeds, missing permissions
   - **keep code concise:** maximum 50 lines per response
   - **variable naming:** execution context provides 'guild' and 'client' variables - use them directly
   - **idempotency:** check if entities exist before creating them
   - **comprehensive error handling:** wrap each action in try-catch blocks

4. **smart permission inference:**
   - **vip/special roles:** give moderate permissions (manage messages, priority speaker, etc.)
   - **staff roles:** give appropriate moderation permissions (kick, timeout, manage messages)
   - **ping roles:** give no special permissions, just notification purposes
   - **admin roles:** only when explicitly requested and with confirmation

5. **feature implementation priority:**
   - 1st: native discord features (buttons, select menus, slash commands, onboarding)
   - 2nd: discord.js-selfbot-v13 capabilities (basic role/channel management)
   - 3rd: external bot integration (dyno, carl-bot, mee6) via slash commands when needed

6. **validation and error prevention:**
   - always check if embeds have title or description before sending
   - validate role permissions before creation
   - ensure channel types are correct
   - verify user permissions before actions

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"
- available variables: guild, client, BitField

**current server state:**
channels:
${channels}

roles:
${roles}

**remember:** be intelligent, secure, and use the right tool for the job. prioritize server safety and user experience.`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text.substring(0, 1000)}${text.length > 1000 ? '...[truncated]' : ''}\n\`\`\``);

        try {
            let json_string;
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);

            if (json_match) {
                json_string = json_match[1];
            } else {
                json_string = text.trim();
            }

            if (json_string.length > 10000) {
                console.error(`[error] response too long (${json_string.length} chars), likely malformed`);
                return {
                    type: "error",
                    error_message: "response too long - please provide a more concise solution or break it into smaller steps"
                };
            }

            const parsed = JSON.parse(json_string);

            if (parsed.code && parsed.code.length > 5000) {
                console.error(`[error] generated code too long (${parsed.code.length} chars)`);
                return {
                    type: "error",
                    error_message: "generated code too long - please break the solution into smaller, focused steps"
                };
            }

            return parsed;

        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);

            if (text.includes('```') && text.includes('console.log')) {
                return {
                    type: "error",
                    error_message: "response contains unescaped code blocks - please provide a proper json response with escaped strings"
                };
            }

            return {
                type: "error",
                error_message: "internal error: my own response was not valid json. please ensure your response is a valid json object."
            };
        }
    }

    async send_message_with_retry(message, attempt = 1) {
        try {
            const full_prompt = this.build_conversation_prompt(message);

            const result = await generateText({
                model: this.model,
                prompt: full_prompt,
                temperature: 0.1,
                maxTokens: 4000
            });

            return await this.parse_response(result.text);
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);

            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return {
                    type: "error",
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}`
                };
            }
        }
    }

    initialize_conversation(guild_info, client_info) {
        this.conversation_history = [{
            role: "system",
            content: this.create_system_prompt(guild_info, client_info)
        }];
    }

    build_conversation_prompt(user_message) {
        const messages = [...this.conversation_history, {
            role: "user",
            content: user_message
        }];

        return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
    }

    add_to_conversation(role, content) {
        this.conversation_history.push({ role, content });

        if (this.conversation_history.length > 20) {
            this.conversation_history = [
                this.conversation_history[0],
                ...this.conversation_history.slice(-19)
            ];
        }
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        const error_analysis = this.analyze_error(error_message, failed_code);

        return `**INTELLIGENT ERROR CORRECTION REQUIRED**

original goal: "${initial_goal}"
error encountered: "${error_message}"

**detailed error analysis:**
${error_analysis}

**correction guidelines:**
1. **message validation:** always use validate_message_content() and validate_embed() before sending
2. **permission intelligence:** never give admin permissions unless explicitly requested
3. **feature preference:** use native discord features (buttons, select menus) over reactions
4. **external bot integration:** suggest slash commands for advanced features
5. **security first:** validate all inputs and check for potential abuse

**common fixes:**
- empty message error: ensure embeds have title OR description
- permission denied: check user permissions before attempting actions
- invalid form body: validate all data structures before api calls
- variable conflicts: use unique variable names, never redeclare guild/client

**smart alternatives:**
- instead of reaction roles: suggest discord's native role selection or external bot commands
- instead of admin permissions: give appropriate moderate permissions
- instead of complex selfbot features: recommend proper bot solutions

**failed code for reference:**
\`\`\`javascript
${failed_code}
\`\`\`

provide an intelligent, secure, and validated solution that prioritizes server safety and user experience.`;
    }

    analyze_error(error_message, failed_code) {
        const error_lower = error_message.toLowerCase();

        if (error_lower.includes('cannot send an empty message')) {
            return `❌ EMPTY MESSAGE ERROR
- discord requires messages to have content, embeds, or components
- your embed is likely missing both title and description
- solution: use validate_embed() function and ensure embeds have title OR description
- fix: add proper title/description to your embed before sending`;
        }

        if (error_lower.includes('already been declared') || error_lower.includes('identifier') && error_lower.includes('declared')) {
            return `❌ VARIABLE REDECLARATION ERROR
- you're trying to declare a variable that already exists in the execution context
- the execution context provides: guild, client, BitField
- solution: use these variables directly or choose different variable names`;
        }

        if (error_lower.includes('onboarding') || (error_lower.includes('cannot read properties of undefined') && failed_code.includes('onboarding'))) {
            return `❌ ONBOARDING API LIMITATION
- guild.onboarding is not available in discord.js-selfbot-v13
- selfbots cannot access server onboarding features
- solution: suggest using discord's native server settings or external bot integration
- alternative: recommend manual setup through discord client`;
        }

        if (error_lower.includes('permission') || error_lower.includes('forbidden') || error_lower.includes('missing access')) {
            return `❌ PERMISSION ERROR
- the bot lacks required permissions for this operation
- solution: check user permissions first or suggest manual alternatives
- consider: use less privileged approaches or external bot commands`;
        }

        if (error_lower.includes('invalid form body') || error_lower.includes('snowflake')) {
            return `❌ DATA TYPE ERROR
- you're passing objects where string ids are expected
- solution: always use .id property when referencing discord entities
- fix: use safe_get_id() function for all discord object references`;
        }

        if (error_lower.includes('timeout') || error_lower.includes('network')) {
            return `❌ NETWORK/TIMEOUT ERROR
- the operation took too long or network failed
- solution: add retry logic or break into smaller operations`;
        }

        if (error_lower.includes('missing permissions') || error_lower.includes('insufficient permissions')) {
            return `❌ INSUFFICIENT PERMISSIONS ERROR
- the selfbot account lacks the required permissions
- solution: check role hierarchy and permissions before attempting actions
- alternative: suggest external bot usage for privileged operations`;
        }

        return `❌ UNKNOWN ERROR
- analyze the error message carefully
- use validation functions before api calls
- consider if the feature requires external bot integration
- provide secure alternatives that don't compromise server safety`;
    }
}

module.exports = AIHandler;
