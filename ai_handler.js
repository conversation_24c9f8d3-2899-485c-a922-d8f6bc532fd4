const { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } = require("@google/generative-ai");

class AIHandler {
    constructor(api_key) {
        this.gen_ai = new GoogleGenerativeAI(api_key);
        this.safety_settings = [
            { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE },
        ];
        this.model = this.gen_ai.getGenerativeModel({ 
            model: "gemini-2.5-flash", 
            safetySettings: this.safety_settings 
        });
        this.max_retries = 3;
        this.retry_delay = 1000;
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;

        return `
you are an expert discord server management ai with advanced self-correction capabilities. your primary function is to generate robust, intelligent, and executable javascript code for the discord.js-selfbot-v13 library.

**critical instructions:**

1.  **action protocol:**
    *   your response **must** be a single, valid json object.
    *   the json must have a 'type' field. the **only** valid values for 'type' are: \`execute\`, \`ask\`, \`inform\`, \`error\`.

2.  **code generation rules:**
    *   the 'code' field must be a **valid json string** (e.g., use "\\n" for newlines).
    *   **keep code concise:** maximum 50 lines of code per response. if more is needed, break into steps.
    *   **do not** wrap your code in an \`async () => { ... }\` block. provide only the raw sequence of statements.
    *   **do not** include javascript comments (e.g., \`// ...\`) in your generated code.
    *   **do not** generate console.log statements that output code - this breaks json parsing.
    *   **variable naming:** the execution context already provides 'guild' and 'client' variables. never redeclare them. use them directly.
    *   **idempotency is mandatory:** your code must be runnable multiple times without causing errors. before creating an entity (role, channel), first check if an entity with that name already exists. before editing, check if it exists.
    *   **granular error handling:** wrap individual, distinct actions in their own \`try...catch\` blocks. this ensures that if one action fails, the script can continue with the next actions.
    *   **snowflake safety:** always use .id property when passing discord objects to api calls. never pass the full object.
    *   **library limitations:** discord.js-selfbot-v13 has limited api support. many features like guild onboarding, slash commands, and advanced moderation tools are not available. focus on basic operations: roles, channels, messages, permissions.

3.  **smart error analysis:**
    *   when you receive an error, analyze it deeply:
        - "already declared" errors mean you're redeclaring existing variables
        - "not found" errors mean the api endpoint doesn't exist in the selfbot library
        - "missing permissions" means the bot lacks required permissions
        - "invalid form body" means you're passing wrong data types
    *   always provide alternative approaches when the primary method fails
    *   if an api feature doesn't exist, suggest manual alternatives or workarounds

4.  **admin best practices & inference:**
    *   you are a server admin assistant, not just a code generator. **infer the user's intent** and apply best practices.
    *   **example inference:** if a user asks for "manager" roles, you should infer they need to be grouped together and displayed separately. therefore, you should automatically set \`hoist: true\` and manage their positions to be logical.
    *   **permissions:** for clarity and correctness, use BigInt literals for permissions (e.g., \`8n\` for Administrator) or use \`new BitField()\` if necessary. for no permissions, use \`[]\` or \`0n\`.

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"
- available variables: guild (the target guild object), client (the discord client), BitField (for permissions)

**library capabilities (discord.js-selfbot-v13):**
- ✅ basic role management (create, edit, delete, assign)
- ✅ channel management (create, edit, delete, permissions)
- ✅ message operations (send, edit, delete)
- ✅ member management (kick, ban, nickname)
- ❌ guild onboarding (not supported in selfbot)
- ❌ slash commands (not supported in selfbot)
- ❌ advanced moderation features
- ❌ server discovery features

**current server state:**
channels:
${channels}

roles:
${roles}

---
`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text.substring(0, 1000)}${text.length > 1000 ? '...[truncated]' : ''}\n\`\`\``);

        try {
            let json_string;
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);

            if (json_match) {
                json_string = json_match[1];
            } else {
                json_string = text.trim();
            }

            if (json_string.length > 10000) {
                console.error(`[error] response too long (${json_string.length} chars), likely malformed`);
                return {
                    type: "error",
                    error_message: "response too long - please provide a more concise solution or break it into smaller steps"
                };
            }

            const parsed = JSON.parse(json_string);

            if (parsed.code && parsed.code.length > 5000) {
                console.error(`[error] generated code too long (${parsed.code.length} chars)`);
                return {
                    type: "error",
                    error_message: "generated code too long - please break the solution into smaller, focused steps"
                };
            }

            return parsed;

        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);

            if (text.includes('```') && text.includes('console.log')) {
                return {
                    type: "error",
                    error_message: "response contains unescaped code blocks - please provide a proper json response with escaped strings"
                };
            }

            return {
                type: "error",
                error_message: "internal error: my own response was not valid json. please ensure your response is a valid json object."
            };
        }
    }

    async send_message_with_retry(chat, message, attempt = 1) {
        try {
            const result = await chat.sendMessage(message);
            return await this.parse_response(result.response.text());
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);
            
            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(chat, message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return { 
                    type: "error", 
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}` 
                };
            }
        }
    }

    create_chat(guild_info, client_info) {
        return this.model.startChat({
            history: [{ role: "user", parts: [{ text: this.create_system_prompt(guild_info, client_info) }] }],
            generationConfig: { temperature: 0.2 }
        });
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        const error_analysis = this.analyze_error(error_message, failed_code);

        return `**ERROR ANALYSIS REQUIRED**

original goal: "${initial_goal}"
error encountered: "${error_message}"

**detailed error analysis:**
${error_analysis}

**your task:**
1. analyze the specific error type and root cause
2. if the feature is not supported in discord.js-selfbot-v13, provide an alternative approach
3. if it's a variable conflict, use different variable names
4. if it's a permission issue, suggest checking permissions first
5. if it's an api limitation, explain why and offer workarounds

**execution context reminders:**
- variables 'guild' and 'client' are already available - never redeclare them
- discord.js-selfbot-v13 has limited api support compared to the full discord.js library
- always check if entities exist before creating them
- wrap each operation in try-catch blocks for granular error handling

**failed code for reference:**
\`\`\`javascript
${failed_code}
\`\`\`

provide a corrected solution or explain why the goal cannot be achieved with the current library limitations.`;
    }

    analyze_error(error_message, failed_code) {
        const error_lower = error_message.toLowerCase();

        if (error_lower.includes('already been declared') || error_lower.includes('identifier') && error_lower.includes('declared')) {
            return `❌ VARIABLE REDECLARATION ERROR
- you're trying to declare a variable that already exists in the execution context
- the execution context provides: guild, client, BitField
- solution: use these variables directly or choose different variable names`;
        }

        if (error_lower.includes('onboarding') || error_lower.includes('not found')) {
            return `❌ API LIMITATION ERROR
- the requested feature is not available in discord.js-selfbot-v13
- selfbots have limited api access compared to regular bots
- solution: provide alternative approaches or explain the limitation`;
        }

        if (error_lower.includes('permission') || error_lower.includes('forbidden')) {
            return `❌ PERMISSION ERROR
- the bot lacks required permissions for this operation
- solution: check permissions first or suggest manual alternatives`;
        }

        if (error_lower.includes('invalid form body') || error_lower.includes('snowflake')) {
            return `❌ DATA TYPE ERROR
- you're passing objects where string ids are expected
- solution: always use .id property when referencing discord entities`;
        }

        if (error_lower.includes('timeout') || error_lower.includes('network')) {
            return `❌ NETWORK/TIMEOUT ERROR
- the operation took too long or network failed
- solution: add retry logic or break into smaller operations`;
        }

        return `❌ UNKNOWN ERROR
- analyze the error message carefully
- check the discord.js-selfbot-v13 documentation for supported features
- provide alternative approaches if the primary method fails`;
    }
}

module.exports = AIHandler;
