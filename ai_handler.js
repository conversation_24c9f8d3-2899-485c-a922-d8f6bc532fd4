const { pipeline } = require('@huggingface/transformers');

class AIHandler {
    constructor() {
        this.pipe = null;
        this.max_retries = 3;
        this.retry_delay = 1000;
        this.conversation_history = [];
        this.tools = this.define_tools();
        this.tool_map = this.create_tool_map();
    }

    async initialize() {
        console.log('initializing local kimi k2 model...');
        this.pipe = await pipeline("text-generation", "moonshotai/Kimi-K2-Instruct", {
            trust_remote_code: true,
            device: 'cpu'
        });
        console.log('kimi k2 model loaded successfully');
    }

    define_tools() {
        return [
            {
                type: "function",
                function: {
                    name: "create_role",
                    description: "Create a new Discord role with specified permissions and properties",
                    parameters: {
                        type: "object",
                        required: ["name"],
                        properties: {
                            name: { type: "string", description: "Role name" },
                            color: { type: "string", description: "Hex color code (e.g., #FFD700)" },
                            permissions: { type: "array", items: { type: "string" }, description: "Permission names" },
                            hoist: { type: "boolean", description: "Display separately in member list" },
                            mentionable: { type: "boolean", description: "Allow role to be mentioned" }
                        }
                    }
                }
            },
            {
                type: "function",
                function: {
                    name: "create_channel",
                    description: "Create a new Discord channel",
                    parameters: {
                        type: "object",
                        required: ["name", "type"],
                        properties: {
                            name: { type: "string", description: "Channel name" },
                            type: { type: "string", description: "Channel type (text, voice, category)" },
                            topic: { type: "string", description: "Channel topic/description" },
                            parent: { type: "string", description: "Parent category name" }
                        }
                    }
                }
            },
            {
                type: "function",
                function: {
                    name: "send_external_bot_command",
                    description: "Send a slash command to an external bot (carl-bot, dyno, etc)",
                    parameters: {
                        type: "object",
                        required: ["command", "channel"],
                        properties: {
                            command: { type: "string", description: "The slash command to send (e.g., '/automod spam enable')" },
                            channel: { type: "string", description: "Channel name to send command in" },
                            description: { type: "string", description: "What this command does" }
                        }
                    }
                }
            },
            {
                type: "function",
                function: {
                    name: "setup_reaction_roles",
                    description: "Create a reaction role system with embed and reactions",
                    parameters: {
                        type: "object",
                        required: ["roles", "channel"],
                        properties: {
                            roles: { type: "array", items: { type: "object" }, description: "Array of role objects with id, name, emoji" },
                            channel: { type: "string", description: "Channel name to post in" },
                            title: { type: "string", description: "Embed title" },
                            description: { type: "string", description: "Embed description" }
                        }
                    }
                }
            }
        ];
    }

    create_tool_map() {
        return {
            create_role: this.tool_create_role.bind(this),
            create_channel: this.tool_create_channel.bind(this),
            send_external_bot_command: this.tool_send_external_bot_command.bind(this),
            setup_reaction_roles: this.tool_setup_reaction_roles.bind(this)
        };
    }

    async tool_create_role(params, discord_handler) {
        const role_data = {
            name: params.name,
            color: params.color ? parseInt(params.color.replace('#', ''), 16) : 0x99AAB5,
            permissions: params.permissions || [],
            hoist: params.hoist || false,
            mentionable: params.mentionable || false
        };

        try {
            const guild = discord_handler.guild;
            const existing = guild.roles.cache.find(r => r.name.toLowerCase() === params.name.toLowerCase());
            if (existing) {
                return { success: true, message: `role '${params.name}' already exists`, role_id: existing.id };
            }

            const role = await guild.roles.create(role_data);
            return { success: true, message: `created role '${params.name}'`, role_id: role.id };
        } catch (error) {
            return { success: false, message: `failed to create role: ${error.message}` };
        }
    }

    async tool_create_channel(params, discord_handler) {
        try {
            const guild = discord_handler.guild;
            const existing = guild.channels.cache.find(c => c.name.toLowerCase() === params.name.toLowerCase());
            if (existing) {
                return { success: true, message: `channel '${params.name}' already exists`, channel_id: existing.id };
            }

            const channel_data = {
                name: params.name,
                type: params.type === 'voice' ? 2 : params.type === 'category' ? 4 : 0,
                topic: params.topic
            };

            if (params.parent) {
                const parent = guild.channels.cache.find(c => c.name.toLowerCase() === params.parent.toLowerCase() && c.type === 4);
                if (parent) channel_data.parent = parent.id;
            }

            const channel = await guild.channels.create(channel_data);
            return { success: true, message: `created channel '${params.name}'`, channel_id: channel.id };
        } catch (error) {
            return { success: false, message: `failed to create channel: ${error.message}` };
        }
    }

    async tool_send_external_bot_command(params, discord_handler) {
        try {
            const guild = discord_handler.guild;
            const channel = guild.channels.cache.find(c => c.name.toLowerCase() === params.channel.toLowerCase());
            if (!channel) {
                return { success: false, message: `channel '${params.channel}' not found` };
            }

            console.log(`[external bot] sending command: ${params.command}`);
            if (params.description) console.log(`[external bot] purpose: ${params.description}`);

            return { success: true, message: `would send command '${params.command}' to ${params.channel}`, note: "command execution disabled to prevent chat spam" };
        } catch (error) {
            return { success: false, message: `failed to send command: ${error.message}` };
        }
    }

    async tool_setup_reaction_roles(params, discord_handler) {
        try {
            const guild = discord_handler.guild;
            const channel = guild.channels.cache.find(c => c.name.toLowerCase() === params.channel.toLowerCase());
            if (!channel) {
                return { success: false, message: `channel '${params.channel}' not found` };
            }

            const embed = {
                title: params.title || 'Reaction Roles',
                description: params.description || 'React to get roles!',
                color: 0x0099ff,
                fields: params.roles.map(role => ({
                    name: `${role.emoji} ${role.name}`,
                    value: 'React to toggle',
                    inline: true
                }))
            };

            if (!embed.title && !embed.description) {
                embed.description = 'React to get roles!';
            }

            const message = await channel.send({ embeds: [embed] });

            for (const role of params.roles) {
                await message.react(role.emoji);
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            console.log(`[reaction roles] created system in ${params.channel} with ${params.roles.length} roles`);
            return { success: true, message: `reaction role system created in ${params.channel}` };
        } catch (error) {
            return { success: false, message: `failed to create reaction roles: ${error.message}` };
        }
    }

    async process_with_tools(user_message, discord_handler) {
        const messages = [
            {
                role: "system",
                content: `you are kimi, an expert discord server management ai. you have access to tools for managing discord servers.

**critical rules:**
- NEVER send chat messages or talk in channels - only use tools for actions
- be concise and direct
- use appropriate permissions (no admin unless requested)
- prefer external bot commands for advanced features
- validate all inputs

**available tools:**
- create_role: make new roles with proper permissions
- create_channel: create text/voice/category channels
- send_external_bot_command: use external bots via slash commands
- setup_reaction_roles: create reaction role systems

**current server:** ${discord_handler.guild.name}

respond with tool calls to accomplish the user's goal. do not send any chat messages.`
            },
            {
                role: "user",
                content: user_message
            }
        ];

        let finish_reason = null;
        let attempts = 0;
        const max_attempts = 5;

        while (finish_reason !== "stop" && attempts < max_attempts) {
            attempts++;

            try {
                const response = await this.pipe(messages, {
                    max_new_tokens: 512,
                    temperature: 0.3,
                    tools: this.tools,
                    tool_choice: "auto"
                });

                const choice = response[0];
                finish_reason = choice.finish_reason;

                if (choice.message && choice.message.tool_calls) {
                    messages.push(choice.message);

                    for (const tool_call of choice.message.tool_calls) {
                        const tool_name = tool_call.function.name;
                        const tool_args = JSON.parse(tool_call.function.arguments);
                        const tool_function = this.tool_map[tool_name];

                        if (tool_function) {
                            console.log(`[tool] executing ${tool_name} with args:`, tool_args);
                            const tool_result = await tool_function(tool_args, discord_handler);
                            console.log(`[tool] result:`, tool_result);

                            messages.push({
                                role: "tool",
                                tool_call_id: tool_call.id,
                                name: tool_name,
                                content: JSON.stringify(tool_result)
                            });
                        } else {
                            console.error(`[tool] unknown tool: ${tool_name}`);
                        }
                    }
                } else if (choice.message && choice.message.content) {
                    console.log(`[ai] ${choice.message.content}`);
                    break;
                }
            } catch (error) {
                console.error(`[ai error] ${error.message}`);
                break;
            }
        }

        return { success: true, message: "goal processing complete" };
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;

        return `you are an expert discord server management ai with advanced intelligence and security awareness. you prioritize server safety, proper permissions, and native discord features.

**critical instructions:**

1. **action protocol:**
   - your response **must** be a single, valid json object
   - the json must have a 'type' field: \`execute\`, \`ask\`, \`inform\`, \`error\`

2. **intelligent decision making:**
   - **permission safety:** give appropriate permissions, not admin unless explicitly requested
   - **feature preference:** use native discord features first, external bots when needed
   - **be concise:** short responses, minimal explanations

3. **code generation rules:**
   - the 'code' field must be a **valid json string** (use "\\n" for newlines)
   - **keep code short:** maximum 30 lines per response
   - **validation required:** always validate embeds and messages before sending
   - **variable naming:** use provided 'guild' and 'client' variables directly
   - **comprehensive error handling:** wrap actions in try-catch blocks

4. **external bot usage:**
   - **never access bot caches** - external bots are separate applications
   - **use direct slash commands** - send commands as regular messages to channels
   - **format:** send "/command parameters" as text message to appropriate channel
   - **examples:** "/automod spam enable", "/role create VIP", "/logging #modlogs"

5. **permission inference:**
   - **vip roles:** moderate permissions (priority speaker, external emojis)
   - **staff roles:** moderation permissions (kick, timeout, manage messages)
   - **ping roles:** no special permissions
   - **admin roles:** only when explicitly requested

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"
- available variables: guild, client, BitField

**current server state:**
channels:
${channels}

roles:
${roles}

**remember:** be concise, secure, and practical. use external bots via slash commands when needed.`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text.substring(0, 1000)}${text.length > 1000 ? '...[truncated]' : ''}\n\`\`\``);

        try {
            let json_string;
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);

            if (json_match) {
                json_string = json_match[1];
            } else {
                json_string = text.trim();
            }

            if (json_string.length > 10000) {
                console.error(`[error] response too long (${json_string.length} chars), likely malformed`);
                return {
                    type: "error",
                    error_message: "response too long - please provide a more concise solution or break it into smaller steps"
                };
            }

            const parsed = JSON.parse(json_string);

            if (parsed.code && parsed.code.length > 5000) {
                console.error(`[error] generated code too long (${parsed.code.length} chars)`);
                return {
                    type: "error",
                    error_message: "generated code too long - please break the solution into smaller, focused steps"
                };
            }

            return parsed;

        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);

            if (text.includes('```') && text.includes('console.log')) {
                return {
                    type: "error",
                    error_message: "response contains unescaped code blocks - please provide a proper json response with escaped strings"
                };
            }

            return {
                type: "error",
                error_message: "internal error: my own response was not valid json. please ensure your response is a valid json object."
            };
        }
    }

    async send_message_with_retry(message, attempt = 1) {
        try {
            const full_prompt = this.build_conversation_prompt(message);

            const result = await generateText({
                model: this.model,
                prompt: full_prompt,
                temperature: 0.1,
                maxTokens: 4000
            });

            return await this.parse_response(result.text);
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);

            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return {
                    type: "error",
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}`
                };
            }
        }
    }

    initialize_conversation(guild_info, client_info) {
        this.conversation_history = [{
            role: "system",
            content: this.create_system_prompt(guild_info, client_info)
        }];
    }

    build_conversation_prompt(user_message) {
        const messages = [...this.conversation_history, {
            role: "user",
            content: user_message
        }];

        return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
    }

    add_to_conversation(role, content) {
        this.conversation_history.push({ role, content });

        if (this.conversation_history.length > 20) {
            this.conversation_history = [
                this.conversation_history[0],
                ...this.conversation_history.slice(-19)
            ];
        }
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        const error_analysis = this.analyze_error(error_message, failed_code);

        return `**ERROR CORRECTION**

goal: "${initial_goal}"
error: "${error_message}"

${error_analysis}

**fix guidelines:**
- validate embeds (title OR description required)
- use external bots via slash commands as text messages
- never access external bot caches
- keep solutions simple and short

**failed code:**
\`\`\`javascript
${failed_code}
\`\`\`

provide a short, working fix.`;
    }

    analyze_error(error_message, failed_code) {
        const error_lower = error_message.toLowerCase();

        if (error_lower.includes('cannot send an empty message')) {
            return `❌ EMPTY MESSAGE ERROR
- discord requires messages to have content, embeds, or components
- your embed is likely missing both title and description
- solution: use validate_embed() function and ensure embeds have title OR description
- fix: add proper title/description to your embed before sending`;
        }

        if (error_lower.includes('already been declared') || error_lower.includes('identifier') && error_lower.includes('declared')) {
            return `❌ VARIABLE REDECLARATION ERROR
- you're trying to declare a variable that already exists in the execution context
- the execution context provides: guild, client, BitField
- solution: use these variables directly or choose different variable names`;
        }

        if (error_lower.includes('onboarding') || (error_lower.includes('cannot read properties of undefined') && failed_code.includes('onboarding'))) {
            return `❌ ONBOARDING API LIMITATION
- guild.onboarding is not available in discord.js-selfbot-v13
- selfbots cannot access server onboarding features
- solution: suggest using discord's native server settings or external bot integration
- alternative: recommend manual setup through discord client`;
        }

        if (error_lower.includes('permission') || error_lower.includes('forbidden') || error_lower.includes('missing access')) {
            return `❌ PERMISSION ERROR
- the bot lacks required permissions for this operation
- solution: check user permissions first or suggest manual alternatives
- consider: use less privileged approaches or external bot commands`;
        }

        if (error_lower.includes('invalid form body') || error_lower.includes('snowflake')) {
            return `❌ DATA TYPE ERROR
- you're passing objects where string ids are expected
- solution: always use .id property when referencing discord entities
- fix: use safe_get_id() function for all discord object references`;
        }

        if (error_lower.includes('timeout') || error_lower.includes('network')) {
            return `❌ NETWORK/TIMEOUT ERROR
- the operation took too long or network failed
- solution: add retry logic or break into smaller operations`;
        }

        if (error_lower.includes('missing permissions') || error_lower.includes('insufficient permissions')) {
            return `❌ INSUFFICIENT PERMISSIONS ERROR
- the selfbot account lacks the required permissions
- solution: check role hierarchy and permissions before attempting actions
- alternative: suggest external bot usage for privileged operations`;
        }

        return `❌ UNKNOWN ERROR
- analyze the error message carefully
- use validation functions before api calls
- consider if the feature requires external bot integration
- provide secure alternatives that don't compromise server safety`;
    }
}

module.exports = AIHandler;
