const { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } = require("@google/generative-ai");

class AIHandler {
    constructor(api_key) {
        this.gen_ai = new GoogleGenerativeAI(api_key);
        this.safety_settings = [
            { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
            { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE },
        ];
        this.model = this.gen_ai.getGenerativeModel({ 
            model: "gemini-2.5-flash", 
            safetySettings: this.safety_settings 
        });
        this.max_retries = 3;
        this.retry_delay = 1000;
    }

    create_system_prompt(guild_info, client_info) {
        const { channels, roles, guild_name, guild_id } = guild_info;
        const { user_tag, user_id } = client_info;
        
        return `
you are an expert discord server management ai. your primary function is to generate robust, intelligent, and executable javascript code for the discord.js-selfbot-v13 library.

**critical instructions:**

1.  **action protocol:**
    *   your response **must** be a single, valid json object.
    *   the json must have a 'type' field. the **only** valid values for 'type' are: \`execute\`, \`ask\`, \`inform\`, \`error\`.

2.  **code generation rules:**
    *   the 'code' field must be a **valid json string** (e.g., use "\\n" for newlines).
    *   **do not** wrap your code in an \`async () => { ... }\` block. provide only the raw sequence of statements.
    *   **do not** include javascript comments (e.g., \`// ...\`) in your generated code.
    *   **idempotency is mandatory:** your code must be runnable multiple times without causing errors. before creating an entity (role, channel), first check if an entity with that name already exists. before editing, check if it exists.
    *   **granular error handling:** wrap individual, distinct actions in their own \`try...catch\` blocks. this ensures that if one action fails, the script can continue with the next actions.
    *   **snowflake safety:** always use .id property when passing discord objects to api calls. never pass the full object.

3.  **admin best practices & inference:**
    *   you are a server admin assistant, not just a code generator. **infer the user's intent** and apply best practices.
    *   **example inference:** if a user asks for "manager" roles, you should infer they need to be grouped together and displayed separately. therefore, you should automatically set \`hoist: true\` and manage their positions to be logical.
    *   **permissions:** for clarity and correctness, use BigInt literals for permissions (e.g., \`8n\` for Administrator) or use \`new BitField()\` if necessary. for no permissions, use \`[]\` or \`0n\`.

**execution context:**
- guild_name: "${guild_name}"
- guild_id: "${guild_id}"
- your_user_tag: "${user_tag}"
- your_user_id: "${user_id}"

**current server state:**
channels:
${channels}

roles:
${roles}

---
`;
    }

    async parse_response(text) {
        console.log(`\n> gemini raw output:\n\`\`\`\n${text}\n\`\`\``);
        try {
            const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);
            const json_string = json_match ? json_match[1] : text;
            return JSON.parse(json_string);
        } catch (error) {
            console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);
            return { type: "error", error_message: "internal error: my own response was not valid json." };
        }
    }

    async send_message_with_retry(chat, message, attempt = 1) {
        try {
            const result = await chat.sendMessage(message);
            return await this.parse_response(result.response.text());
        } catch (error) {
            console.error(`[ai error] attempt ${attempt} failed: ${error.message}`);
            
            if (attempt < this.max_retries) {
                console.log(`[ai retry] retrying in ${this.retry_delay}ms... (attempt ${attempt + 1}/${this.max_retries})`);
                await new Promise(resolve => setTimeout(resolve, this.retry_delay));
                return await this.send_message_with_retry(chat, message, attempt + 1);
            } else {
                console.error(`[ai error] max retries reached. giving up.`);
                return { 
                    type: "error", 
                    error_message: `ai communication failed after ${this.max_retries} attempts: ${error.message}` 
                };
            }
        }
    }

    create_chat(guild_info, client_info) {
        return this.model.startChat({
            history: [{ role: "user", parts: [{ text: this.create_system_prompt(guild_info, client_info) }] }],
            generationConfig: { temperature: 0.2 }
        });
    }

    create_error_correction_prompt(initial_goal, error_message, failed_code) {
        return `the last code you provided failed with this error: "${error_message}".

the original goal was: "${initial_goal}".

please analyze the error and the code. remember that some parts of the previous attempt might have already succeeded. your new code must check for existing roles/channels before trying to create or modify them again to avoid errors. 

**critical fix needed:** ensure all discord api calls use string ids, not objects. always use .id property when referencing discord entities.

provide a new, corrected version of the code. the code that failed was:
\`\`\`javascript
${failed_code}
\`\`\``;
    }
}

module.exports = AIHandler;
