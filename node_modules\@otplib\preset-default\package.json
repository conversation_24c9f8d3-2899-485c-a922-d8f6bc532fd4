{"name": "@otplib/preset-default", "description": "basic preset for otplib", "version": "12.0.1", "main": "./index.js", "publishConfig": {"access": "public"}, "scripts": {}, "keywords": ["otplib-preset", "node"], "dependencies": {"@otplib/core": "^12.0.1", "@otplib/plugin-crypto": "^12.0.1", "@otplib/plugin-thirty-two": "^12.0.1"}, "otplib": {}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://yeojz.otplib.dev", "repository": "https://github.com/yeojz/otplib/tree/master/packages/otplib-preset-default", "types": "./index.d.ts"}