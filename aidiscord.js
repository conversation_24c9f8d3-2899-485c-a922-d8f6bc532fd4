const inquirer = require('inquirer').default;
const AIHandler = require('./ai_handler');
const DiscordHandler = require('./discord_handler');

const discord_token = "MTAwNjE5OTIyNTc0MDE4NTczMg.GRub0q.ZSC2otUrTRyaz2dT4FAdBP-xhV0RSr6zyn-56E";
const gemini_api_key = "AIzaSyB-za5Jmj7zAynijLTSnNkqKLtMM9NPD44";
const server_id = "1386782892612391022";

const ai_handler = new AIHandler(gemini_api_key);
const discord_handler = new DiscordHandler(discord_token, server_id);



async function process_goal(initial_goal) {
    console.log(`\nprocessing new goal: "${initial_goal}"`);

    const guild_info = discord_handler.get_server_state();
    const client_info = discord_handler.get_client_info();
    const chat = ai_handler.create_chat(guild_info, client_info);

    let current_input = `user's goal: "${initial_goal}"`;
    let conversation_active = true;
    let last_code_attempt = "";
    let retry_count = 0;
    const max_retries = 3;

    while (conversation_active) {
        try {
            const response_object = await ai_handler.send_message_with_retry(chat, current_input);

            switch (response_object.type) {
                case "execute":
                    last_code_attempt = response_object.code;
                    console.log(`[ai reason] ${response_object.reason || '(not provided)'}`);

                    try {
                        const execution_result = await discord_handler.execute_code_safely(response_object.code);
                        current_input = `action result: ${execution_result}. the server state is updated. determine the next logical step to complete the goal.`;
                        retry_count = 0;
                    } catch (exec_error) {
                        throw exec_error;
                    }
                    break;

                case "ask":
                    const { answer } = await inquirer.prompt([{
                        type: 'input',
                        name: 'answer',
                        message: `[ai question] ${response_object.question}`
                    }]);
                    current_input = `user's answer: "${answer}"`;
                    break;

                case "inform":
                    console.log(`\n[ai message] ${response_object.message}`);
                    console.log("--- goal complete ---");
                    conversation_active = false;
                    break;

                case "error":
                    console.error(`\n[ai error] ${response_object.error_message}`);
                    conversation_active = false;
                    break;

                default:
                    const invalid_type = response_object.type || "(not provided)";
                    console.log(`[system] ai used an invalid action type: '${invalid_type}'. asking for correction.`);
                    current_input = `your last response was structurally incorrect. you used an invalid 'type': "${invalid_type}". the only valid types are 'execute', 'ask', 'inform', 'error'. please re-evaluate the original goal and provide a new, valid json object.`;
                    break;
            }
        } catch (error) {
            retry_count++;
            console.error(`[error] attempt ${retry_count} failed: ${error.message}`);

            if (retry_count >= max_retries) {
                console.error(`[error] max retries (${max_retries}) reached. stopping goal processing.`);
                conversation_active = false;
                break;
            }

            current_input = ai_handler.create_error_correction_prompt(initial_goal, error.message, last_code_attempt);
            console.log(`[system] asking ai for correction (attempt ${retry_count + 1}/${max_retries})...`);
        }
    }
}

async function start_interaction_loop() {
    console.log('\n--- discord server management ai ---');
    console.log('state your goal, or type "exit" to close.');

    while (true) {
        try {
            const { goal } = await inquirer.prompt([{
                type: 'input',
                name: 'goal',
                message: 'new goal >'
            }]);

            if (['exit', 'quit', 'q'].includes(goal.toLowerCase())) break;
            if (goal.trim()) await process_goal(goal);
        } catch (error) {
            console.error(`[error] interaction failed: ${error.message}`);
            console.log('continuing with next goal...');
        }
    }
    console.log('exiting...');
    discord_handler.destroy();
}

async function main() {
    try {
        console.log('initializing discord handler...');
        await discord_handler.initialize();
        console.log('discord handler ready. starting interaction loop...');
        await start_interaction_loop();
    } catch (error) {
        console.error(`initialization failed: ${error.message}`);
        console.error('please ensure your discord_token is correct and you have access to the specified server.');
        process.exit(1);
    }
}

main().catch(error => {
    console.error(`an unexpected error occurred: ${error.message}`);
    discord_handler.destroy();
    process.exit(1);
});