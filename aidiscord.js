const { Client, BitField } = require('discord.js-selfbot-v13');
const { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } = require("@google/generative-ai");
const inquirer = require('inquirer').default; // Replaced readline with inquirer

// --- configuration ---
// warning: self-bots are against discord's tos and can result in account termination.
// use at your own risk. it is recommended to use a secondary account.
const discord_token = "MTAwNjE5OTIyNTc0MDE4NTczMg.GRub0q.ZSC2otUrTRyaz2dT4FAdBP-xhV0RSr6zyn-56E"; // place your discord token here
const gemini_api_key = "AIzaSyB-za5Jmj7zAynijLTSnNkqKLtMM9NPD44"; // place your google gemini api key here
const server_id = "1386782892612391022"; // place the id of the server you want to manage

// --- initial setup ---
const client = new Client({ checkUpdate: false });
const gen_ai = new GoogleGenerativeAI(gemini_api_key);
const safety_settings = [
    { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE },
];
const model = gen_ai.getGenerativeModel({ model: "gemini-2.5-flash", safetySettings: safety_settings });

let guild;

// --- core functions ---

function get_server_state() {
    const channels = guild.channels.cache.map(c => `- ${c.name} (id: ${c.id}, type: ${c.type.toString().replace('GUILD_', '').toLowerCase()})`).join('\n');
    const roles = guild.roles.cache.map(r => `- ${r.name} (id: ${r.id}, position: ${r.position}, hoisted: ${r.hoist})`).join('\n'); // Added position and hoist
    return { channels, roles };
}

function create_system_prompt() {
    const { channels, roles } = get_server_state();
    return `
you are an expert discord server management ai. your primary function is to generate robust, intelligent, and executable javascript code for the discord.js-selfbot-v13 library.

**critical instructions:**

1.  **action protocol:**
    *   your response **must** be a single, valid json object.
    *   the json must have a 'type' field. the **only** valid values for 'type' are: \`execute\`, \`ask\`, \`inform\`, \`error\`.

2.  **code generation rules:**
    *   the 'code' field must be a **valid json string** (e.g., use "\\n" for newlines).
    *   **do not** wrap your code in an \`async () => { ... }\` block. provide only the raw sequence of statements.
    *   **do not** include javascript comments (e.g., \`// ...\`) in your generated code.
    *   **idempotency is mandatory:** your code must be runnable multiple times without causing errors. before creating an entity (role, channel), first check if an entity with that name already exists. before editing, check if it exists.
    *   **granular error handling:** wrap individual, distinct actions in their own \`try...catch\` blocks. this ensures that if one action fails, the script can continue with the next actions.

3.  **admin best practices & inference:**
    *   you are a server admin assistant, not just a code generator. **infer the user's intent** and apply best practices.
    *   **example inference:** if a user asks for "manager" roles, you should infer they need to be grouped together and displayed separately. therefore, you should automatically set \`hoist: true\` and manage their positions to be logical.
    *   **permissions:** for clarity and correctness, use BigInt literals for permissions (e.g., \`8n\` for Administrator) or use \`new BitField()\` if necessary. for no permissions, use \`[]\` or \`0n\`.

**execution context:**
- guild_name: "${guild.name}"
- guild_id: "${guild.id}"
- your_user_tag: "${client.user.tag}"
- your_user_id: "${client.user.id}"

**current server state:**
channels:
${channels}

roles:
${roles}

---
`;
}

async function parse_gemini_response(text) {
    console.log(`\n> gemini raw output:\n\`\`\`\n${text}\n\`\`\``);
    try {
        const json_match = text.match(/```json\s*([\s\S]*?)\s*```/);
        const json_string = json_match ? json_match[1] : text;
        return JSON.parse(json_string);
    } catch (error) {
        console.error(`[error] failed to parse json from gemini response. error: ${error.message}`);
        return { type: "error", error_message: "internal error: my own response was not valid json." };
    }
}

async function execute_code(code_string) {
    console.log(`\n> generated code:\n\`\`\`javascript\n${code_string}\n\`\`\``);

    let captured_logs = [];
    const original_console_log = console.log;
    console.log = (...args) => {
        const message = args.map(a => (typeof a === 'object' ? JSON.stringify(a, null, 2) : String(a))).join(' ');
        captured_logs.push(message);
        original_console_log.apply(console, ['[code log]', ...args]);
    };

    try {
        await eval(`(async () => { ${code_string} })();`);
        console.log = original_console_log;
        const result_message = "code executed successfully.";
        return captured_logs.length > 0 ? `${result_message} console output: ${captured_logs.join('\n')}` : result_message;
    } catch (error) {
        console.log = original_console_log;
        throw error;
    }
}

async function process_goal(initial_goal) {
    console.log(`\nprocessing new goal: "${initial_goal}"`);
    const chat = model.startChat({
        history: [{ role: "user", parts: [{ text: create_system_prompt() }] }],
        generationConfig: { temperature: 0.2 }
    });

    let current_input = `user's goal: "${initial_goal}"`;
    let conversation_active = true;
    let last_code_attempt = "";

    while (conversation_active) {
        try {
            const result = await chat.sendMessage(current_input);
            const response_object = await parse_gemini_response(result.response.text());

            switch (response_object.type) {
                case "execute":
                    last_code_attempt = response_object.code;
                    console.log(`[ai reason] ${response_object.reason || '(not provided)'}`);
                    const execution_result = await execute_code(response_object.code);
                    await guild.members.fetch({ force: true });
                    await guild.roles.fetch({ force: true });
                    await guild.channels.fetch({ force: true });
                    current_input = `action result: ${execution_result}. the server state is updated. determine the next logical step to complete the goal.`;
                    break;

                case "ask":
                    const { answer } = await inquirer.prompt([{ type: 'input', name: 'answer', message: `[ai question] ${response_object.question}` }]);
                    current_input = `user's answer: "${answer}"`;
                    break;

                case "inform":
                    console.log(`\n[ai message] ${response_object.message}`);
                    console.log("--- goal complete ---");
                    conversation_active = false;
                    break;

                case "error":
                    console.error(`\n[ai error] ${response_object.error_message}`);
                    conversation_active = false;
                    break;

                default:
                    const invalid_type = response_object.type || "(not provided)";
                    console.log(`[system] ai used an invalid action type: '${invalid_type}'. asking for correction.`);
                    current_input = `your last response was structurally incorrect. you used an invalid 'type': "${invalid_type}". the only valid types are 'execute', 'ask', 'inform', 'error'. please re-evaluate the original goal and provide a new, valid json object.`;
                    break;
            }
        } catch (error) {
            console.error(`[error] an error occurred during code execution: ${error.message}`);
            // --- ENHANCED SELF-CORRECTION FOR CODE EXECUTION ERROR ---
            current_input = `the last code you provided failed with this error: "${error.message}".\n\nthe original goal was: "${initial_goal}".\n\nplease analyze the error and the code. remember that some parts of the previous attempt might have already succeeded. your new code must check for existing roles/channels before trying to create or modify them again to avoid errors. provide a new, corrected version of the code. the code that failed was:\n\`\`\`javascript\n${last_code_attempt}\n\`\`\``;
            console.log('[system] asking ai for a code correction...');
        }
    }
}

async function start_interaction_loop() {
    console.log('\n--- discord server management ai ---');
    console.log('state your goal, or type "exit" to close.');

    while (true) {
        const { goal } = await inquirer.prompt([{ type: 'input', name: 'goal', message: 'new goal >' }]);
        if (['exit', 'quit', 'q'].includes(goal.toLowerCase())) break;
        if (goal) await process_goal(goal);
    }
    console.log('exiting...');
    client.destroy();
}

async function main() {
    client.on('ready', async () => {
        console.log(`logged in as ${client.user.tag}`);
        guild = client.guilds.cache.get(server_id);
        if (!guild) {
            console.error(`error: could not find server with id: ${server_id}`);
            process.exit(1);
        }
        console.log(`successfully attached to server: "${guild.name}" (id: ${guild.id})`);

        try {
            console.log('fetching initial server data...');
            await guild.members.fetch();
            await guild.roles.fetch();
            await guild.channels.fetch();
            console.log('server data fetched. ai is ready.');
            await start_interaction_loop();
        } catch (error) {
            console.error(`failed to fetch server data: ${error.message}`);
            process.exit(1);
        }
    });

    await client.login(discord_token).catch(error => {
        console.error(`failed to log in to discord: ${error.message}`);
        console.error('please ensure your discord_token is correct and you are not being rate-limited.');
        process.exit(1);
    });
}

main().catch(error => {
    console.error(`an unexpected error occurred: ${error.message}`);
    process.exit(1);
});