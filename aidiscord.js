const inquirer = require('inquirer').default;
const AIHandler = require('./ai_handler');
const DiscordHandler = require('./discord_handler');

const discord_token = "MTAwNjE5OTIyNTc0MDE4NTczMg.GRub0q.ZSC2otUrTRyaz2dT4FAdBP-xhV0RSr6zyn-56E";
const server_id = "1386782892612391022";

const ai_handler = new AIHandler();
const discord_handler = new DiscordHandler(discord_token, server_id);



async function process_goal(initial_goal) {
    console.log(`\nprocessing: "${initial_goal}"`);

    try {
        const result = await ai_handler.process_with_tools(initial_goal, discord_handler);
        if (result.success) {
            console.log("--- complete ---");
        } else {
            console.error(`[failed] ${result.message}`);
        }
    } catch (error) {
        console.error(`[error] ${error.message}`);
    }
}

async function start_interaction_loop() {
    console.log('\n--- discord server management ai ---');
    console.log('state your goal, or type "exit" to close.');

    while (true) {
        try {
            const { goal } = await inquirer.prompt([{
                type: 'input',
                name: 'goal',
                message: 'new goal >'
            }]);

            if (['exit', 'quit', 'q'].includes(goal.toLowerCase())) break;
            if (goal.trim()) await process_goal(goal);
        } catch (error) {
            console.error(`[error] interaction failed: ${error.message}`);
            console.log('continuing with next goal...');
        }
    }
    console.log('exiting...');
    discord_handler.destroy();
}

async function main() {
    try {
        console.log('initializing discord handler...');
        await discord_handler.initialize();
        console.log('initializing ai handler...');
        await ai_handler.initialize();
        console.log('ready. starting interaction loop...');
        await start_interaction_loop();
    } catch (error) {
        console.error(`initialization failed: ${error.message}`);
        process.exit(1);
    }
}

main().catch(error => {
    console.error(`an unexpected error occurred: ${error.message}`);
    discord_handler.destroy();
    process.exit(1);
});