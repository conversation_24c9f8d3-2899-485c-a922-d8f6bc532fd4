# @otplib/preset-default

> otplib node bundle with pre-configured plugins to get started quickly.

See our [project readme][project-v-readme] for more information
or visit the [demo website][project-v-site].

## Install

```bash
npm install --save @otplib/preset-default
```

## License

`@otplib/preset-default` is [MIT licensed][project-license]

[project-license]: https://github.com/yeojz/otplib/blob/master/LICENSE
[project-v-readme]: https://github.com/yeojz/otplib/blob/master/README.md
[project-v-site]: https://otplib.yeojz.dev
