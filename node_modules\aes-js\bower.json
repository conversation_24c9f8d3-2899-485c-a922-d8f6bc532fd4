{"name": "aes-js", "description": "A pure JavaScript implementation of the AES block cipher and all common modes of operation.", "main": "index.js", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["aes", "aes-ctr", "aes-ofb", "aes-ecb", "aes-cbc", "aes-cfb", "encrypt", "decrypt", "block", "cipher"], "homepage": "https://github.com/ricmoo/aes-js", "moduleType": ["globals"], "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}